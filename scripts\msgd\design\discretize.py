import copy
import logging

import numpy as np
from meshio import CellBlock
from rich.pretty import pretty_repr

from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
from multiprocessing import Manager

import msgd._global as GLOBAL
from msgd.core._structure_model import StructureModel
from msgd.core._data_classes import DataBase, EntitySet
from msgd.core._helpers import *
from msgd.design.distribution import Distribution
from ._helpers import process_element

logger = logging.getLogger(__name__)


def eval_distributions_on_region_nodes(
    distributions:list[Distribution],
    mesh_cells:list[CellBlock],
    cell_data_etags:list[np.ndarray],
    nodes:np.ndarray,
    regions:dict[str, EntitySet]
    ) -> dict:
    """Evaluate distributions on nodes in their regions.

    For each distribution, only nodes in the distribution's region are evaluated.
    Other nodes are set to np.nan.

    Parameters
    ----------
    distributions : list
        List of distributions to evaluate.

    mesh_cells : list of CellBlock
        List of cell blocks.
        Structure:

        ..  code-block::

            [
                CellBlock('triangle', [[0, 1, 2], [1, 2, 3], ...]),
                Cell<PERSON><PERSON>('quad', [[11, 12, 13, 14], [15, 16, 17, 18], ...]),
                ...
            ]

    cell_data_etags : list of ndarray
        List of element IDs for each cell block.
        Structure:

        ..  code-block::

            [
                [1, 2, 3, ...],
                [101, 102, 103, ...],
                ...
            ]

    nodes : ndarray
        List of all nodes.
        Structure:

        ..  code-block::

            [
                [0.0, 0.0, 0.0],
                [1.0, 0.0, 0.0],
                ...
            ]

    regions : dict
        Dictionary of regions.
        Structure:

        ..  code-block::

            {
                'region1': EntitySet('region1', 'element', [1, 2, 3, ...]),
                'region2': EntitySet('region2', 'element', [101, 102, 103, ...]),
                ...
            }

    Returns
    -------
    dict
        Dictionary of nodal values of parameters.
        Each key is a parameter name, and the value is an array of nodal values.
        The array has the same length as the number of nodes.
        Nodal values are ordered the same as the input nodes array.
        Nodal values for nodes not in the region are set to np.nan.
        Structure:

        ..  code-block::

            {
                'param1': np.array([value1, value2, ...]),
                'param2': np.array([value1, value2, ...]),
                ...
            }

    Example
    -------
    ..  code-block:: python
        
        >>> # Linear function: f(x) = 2x + 1
        >>> # Quadratic function: f(x) = x^2
        >>> distr1 = Distribution('param1', 'region1', func_base=linear_function)
        >>> distr2 = Distribution('param2', 'region2', func_base=quadratic_function)
        >>> distributions = [distr1, distr2]
        >>> points = np.array([
        ...     [0, 0], [1, 0], [2, 0],
        ...     [0, 1], [1, 1], [2, 1]
        ... ])
        >>> cells = [
        ...     CellBlock('triangle', [[1, 5, 4], [1, 2, 5]]),
        ...     CellBlock('quad', [[0, 1, 4, 3], ])
        ... ]
        >>> cell_data_etags = [
        ...     [2, 3],
        ...     [1]
        ... ]
        >>> regions = {
        ...     'region1': EntitySet('region1', 'element', [1, 2, 3]),
        ...     'region2': EntitySet('region2', 'element', [1,])
        ... }
        >>> eval_distributions_on_region_nodes(distributions, cells, cell_data_etags, points, regions)
        {
            'param1': np.array([1.0, 3.0, 5.0, 1.0, 3.0, 5.0]),
            'param2': np.array([0.0, 1.0, np.nan, 0.0, 1.0, np.nan])
        }
    """

    # 1. Initialize result dictionary to store parameter values for all nodes
    # Each parameter will have an array of length equal to number of nodes
    # with np.nan for nodes not in any region
    node_param_values = {}
    num_nodes = len(nodes)

    # 2. For each distribution, process its assigned region
    for distr in distributions:
        # 2.1 Get elements in the distribution's region
        region_etags = regions[distr.region].items

        # 2.2 Create mask for selected elements using helper function
        element_masks = mask_selected_elements(cell_data_etags, region_etags)

        # 2.3 For each cell block, get unique nodes of selected elements
        all_region_node_ids = set()
        for cell_idx, (cell_block, mask) in enumerate(zip(mesh_cells, element_masks)):
            # Get unique node IDs from selected elements in this cell block
            unique_node_ids = get_unique_ntags_of_selected_elements(
                cell_block.data, mask)
            all_region_node_ids.update(unique_node_ids)

        # 2.4 Convert node IDs to array and get corresponding coordinates
        region_node_ids = np.array(list(all_region_node_ids))
        region_node_coords = nodes[region_node_ids]

        # 2.5 Vectorized evaluation of distribution on all region nodes
        param_values = calc_params_on_nodes(distr, region_node_coords)

        # 2.6 Store results in the output dictionary
        param_names = [distr.name] if not isinstance(distr.name, list) else distr.name
        for i, param_name in enumerate(param_names):
            # Initialize parameter array with np.nan for all nodes if not exists
            if param_name not in node_param_values:
                node_param_values[param_name] = np.full(num_nodes, np.nan)

            # Vectorized assignment: set values for all nodes in this region at once
            node_param_values[param_name][region_node_ids] = param_values[:, i]

    # 3. Return the complete dictionary of node parameter values
    return node_param_values






# def evaluate_distribution(distribution, node_coords, node_ids):
#     """Evaluate a distribution for all nodes at once."""
#     # Ensure node_coords is properly shaped for vectorized evaluation
#     if len(node_coords.shape) == 1:
#         node_coords = node_coords.reshape(1, -1)
        
#     # Call distribution with all nodes at once
#     results = []
#     for i, nid in enumerate(node_ids):
#         # Still call one by one because Distribution.__call__ might not support vectorization
#         result = distribution([node_coords[i]], _id=nid)
        
#         # Convert result to array format
#         param_names = [distribution.name] if not isinstance(distribution.name, list) else distribution.name
#         param_values = []
#         for param_name in param_names:
#             param_values.append(result[param_name])
            
#         results.append(param_values)
        
#     return results


def calc_params_on_nodes(distribution:Distribution, nodes:np.ndarray):
    """Calculate parameter values on given nodal coordinates.

    Parameters
    ----------
    distribution:Distribution
        Distribution object.
    nodes:array-like, shape (npoints, ndims)
        Nodal coordinates.

    Returns
    -------
    array-like, shape (npoints, nparams)
        Parameter values at every node.
    """
    # Convert nodes to numpy array and ensure proper shape
    nodes = np.asarray(nodes, dtype=float)
    if nodes.ndim != 2:
        raise ValueError("Input nodes must be 2D array")
    # if nodes.ndim == 1:
    #     nodes = nodes.reshape(1, -1)
    # elif nodes.ndim > 2:
    #     raise ValueError("Input nodes must be 1D or 2D array")

    # npoints = nodes.shape[0]

    # Get parameter names from the distribution
    # if isinstance(distribution.name, list):
    #     param_names = distribution.name
    # else:
    #     param_names = [distribution.name]

    # nparams = len(param_names)

    # Evaluate the distribution at all nodes
    # The distribution.__call__ method returns a dictionary by default
    result = distribution(nodes, return_dict=False)

    # # Convert dictionary results to numpy array with shape (npoints, nparams)
    # result = np.empty((npoints, nparams), dtype=float)

    # for i, param_name in enumerate(param_names):
    #     if param_name in param_values_dict:
    #         param_values = param_values_dict[param_name]
    #         # Handle scalar values (broadcast to all points)
    #         if np.isscalar(param_values):
    #             result[:, i] = param_values
    #         else:
    #             # Handle array values
    #             param_values = np.asarray(param_values)
    #             if param_values.size == 1:
    #                 result[:, i] = param_values.item()
    #             elif param_values.size == npoints:
    #                 result[:, i] = param_values.ravel()
    #             else:
    #                 raise ValueError(f"Parameter '{param_name}' values size {param_values.size} "
    #                                f"does not match number of points {npoints}")
    #     else:
    #         raise ValueError(f"Parameter '{param_name}' not found in distribution results")

    return result


def _process_node_assignments(structure_model, _sg_assign, db_sg_model, db_sg_model_sets):
    """Process node-based SG assignments.
    
    Parameters
    ----------
    _sg_assign : SGAssignment
        The SG assignment to process
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase
        Database of specific SG models
    """
    _nids_done = []
    for _eid, _en_sg_params in structure_model._discrete_sg_params.items():
        for _nid, _nparam in _en_sg_params.items():
            if not _nid in _sg_assign.region_entities or _nid in _nids_done:
                continue
                
            _sg_name = _nparam['sg']
            _sp_sg_model_name = structure_model._getSpecificSgModelName(
                _nparam['sg'], _nparam['params'],
                db_sg_model, db_sg_model_sets
            )

            if not _sp_sg_model_name in structure_model._entity_sets:
                structure_model._entity_sets[_sp_sg_model_name] = {
                    'type': 'node',
                    'items': []
                }
            structure_model._entity_sets[_sp_sg_model_name]['items'].append(_nid)

            _sg_assign.addDiscreteSGAssign(_nid, _sp_sg_model_name)
            _nids_done.append(_nid)


def _process_element_assignments(structure_model, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets):
    """Process element-based SG assignments.
    
    Parameters
    ----------
    _sg_assign : SGAssignment
        The SG assignment to process
    _elem_data : dict
        Dictionary to store element data
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase
        Database of specific SG models
    """
    for _eid in _sg_assign.region_entities:
        logger.debug('-'*8)
        logger.debug(f'element {_eid}')

        _en_sg_params = structure_model._discrete_sg_params[_eid]

        if _sg_assign.location == 'element':
            structure_model._process_whole_element(_eid, _en_sg_params, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets)
        elif _sg_assign.location == 'element_node':
            structure_model._process_element_nodes(_eid, _en_sg_params, _sg_assign, db_sg_model, db_sg_model_sets)


def _process_whole_element(structure_model, _eid, _en_sg_params, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets):
    """Process an element as a whole unit.
    
    Parameters
    ----------
    _eid : int
        Element ID
    _en_sg_params : dict
        Parameters for the element's nodes
    _sg_assign : SGAssignment
        The SG assignment
    _elem_data : dict
        Dictionary to store element data
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase
        Database of specific SG models
    """
    _esg = ''
    _eparams = {}
    
    # Collect parameters from all nodes
    for _nid, _nparam in _en_sg_params.items():
        _esg = _nparam['sg']
        for _pname, _pvalue in _nparam['params'].items():
            if _pname not in _eparams:
                _eparams[_pname] = []
            _eparams[_pname].append(_pvalue)

    # Average parameters across nodes
    for _pname, _pvalue in _eparams.items():
        _ptype = structure_model._get_parameter_type(_pname)
        _eparams[_pname] = structure_model._average_parameter_values(_pvalue, _ptype)
        
        if _pname not in _elem_data:
            _elem_data[_pname] = {}
        _elem_data[_pname][_eid] = _eparams[_pname]

    # Create specific SG model
    _sp_sg_model_name = structure_model._getSpecificSgModelName(
        _esg, _eparams, db_sg_model, db_sg_model_sets
    )

    # Update entity sets and assignments
    if _sp_sg_model_name not in structure_model._entity_sets:
        structure_model._entity_sets[_sp_sg_model_name] = {
            'type': 'element',
            'items': []
        }
    structure_model._entity_sets[_sp_sg_model_name]['items'].append(_eid)
    _sg_assign.addDiscreteSGAssign(_eid, _sp_sg_model_name)


def _process_element_nodes(structure_model, _eid, _en_sg_params, _sg_assign, db_sg_model, db_sg_model_sets):
    """Process individual nodes of an element.
    
    Parameters
    ----------
    _eid : int
        Element ID
    _en_sg_params : dict
        Parameters for the element's nodes
    _sg_assign : SGAssignment
        The SG assignment
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase
        Database of specific SG models
    """
    _sg_model_names = []
    for _nid, _nparam in _en_sg_params.items():
        _sp_sg_model_name = structure_model._getSpecificSgModelName(
            _nparam['sg'], _nparam['params'],
            db_sg_model, db_sg_model_sets
        )
        _sg_model_names.append(_sp_sg_model_name)

    _sg_assign.addDiscreteSGAssign(_eid, _sp_sg_model_name)



def process_sg_assignments(structure_model, db_sg_model, db_sg_model_sets=None):
    """Process SG assignments and create parameter sets.
    
    Parameters
    ----------
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase, optional
        Database of specific SG models
    """
    _elem_data = {}
    
    # Calculate parameters at each node
    structure_model.calcParamsFromDistributions(max_workers=1)

    # Process each SG assignment
    for _sg_assign in structure_model._design.sg_assignments:
        logger.debug('-'*16)
        logger.debug(f'{_sg_assign.region} ({_sg_assign.location}): {_sg_assign.region_entities}')

        if _sg_assign.location == 'node':
            _process_node_assignments(
                structure_model, _sg_assign, db_sg_model, db_sg_model_sets)
        elif _sg_assign.location.startswith('element'):
            _process_element_assignments(
                structure_model, _sg_assign, _elem_data, db_sg_model, db_sg_model_sets)

    # Store the data for visualization
    structure_model.addDataToMesh(data=_elem_data, loc='element')


def calcParamsFromDistributions(
    structure_model:StructureModel, max_workers=None
    ) -> None:
    """
    Calculate specific values of parameters for each element and node
    using the distributions.

    Parameters
    ----------
    max_workers : int, optional
        Maximum number of parallel processes to use. If None, uses the default
        (number of CPU cores). If 1, runs in serial mode.

    Update the `_discrete_sg_params` attribute.
    """
    logger.debug('')
    logger.info(f'[{structure_model.name}] calculating parameters from distributions...')
    logger.info(f'Using {max_workers if max_workers else "default"} parallel processes')

    # Get mesh nodes and elements
    _nodes = {}
    if structure_model.mesh_nodes_pd:
        _nodes = copy.deepcopy(structure_model.mesh_nodes_pd)
    else:
        _nodes = copy.deepcopy(structure_model.getMeshNodes())

        # Transform
        if GLOBAL.VARIANT == 'ivabs':
            # For iVABS, by default, use x1 to evaluate distributions
            for _nid, _ncoords in _nodes.items():
                _nodes[_nid] = _ncoords[:1]

    _elements = structure_model.getMeshElements()
    _entity_sets = structure_model.getEntitySets()
    logger.debug(f'_entity_sets = {_entity_sets}')

    # Pre-calculate region assignments for elements
    element_regions = {}
    for _set_name, _set in _entity_sets.items():
        if _set['type'] == 'element':
            for _eid in _set['items']:
                if _eid not in element_regions:
                    element_regions[_eid] = []
                element_regions[_eid].append(_set_name)

    # Pre-calculate SG assignments for regions
    region_sg_assigns = {}
    for _sa in structure_model.sg_assignments:
        if _sa.region == 'all':
            region_sg_assigns['all'] = _sa
        else:
            region_sg_assigns[_sa.region] = _sa

    # Pre-calculate distributions for regions
    region_distributions = {}
    for _distr in structure_model.design.distributions:
        if _distr.region == 'all':
            if 'all' not in region_distributions:
                region_distributions['all'] = []
            region_distributions['all'].append(_distr)
        else:
            if _distr.region not in region_distributions:
                region_distributions[_distr.region] = []
            region_distributions[_distr.region].append(_distr)

    # Create a manager for shared data
    manager = Manager()
    _region_node_values = manager.dict()
    _cache_lock = manager.Lock()  # Create a proper lock object

    # Use ProcessPoolExecutor for parallel processing
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all elements for processing
        future_to_element = {
            executor.submit(
                process_element,
                _eid, _enodes, _nodes, element_regions, region_sg_assigns, 
                region_distributions, _region_node_values, _cache_lock
            ): _eid 
            for _eid, _enodes in _elements.items()
        }

        # Collect results as they complete
        logger.info(f'Processing {len(_elements)} elements...')
        for future in as_completed(future_to_element):
            _eid = future_to_element[future]
            try:
                element_data = future.result()
                logger.debug(f'Element {_eid} processed successfully')
                logger.debug(f'Element {_eid} data: {element_data}')
                structure_model._discrete_sg_params.update(element_data)
            except Exception as e:
                logger.error(f'Error processing element {_eid}: {str(e)}')
                raise


def discretize_design_old(
    structure_model:StructureModel, db_sg_model, db_sg_model_sets:DataBase=None
    ) -> None:
    """Calculate the discrete SG designs based on the structural mesh.

    Parameters
    ----------
    structure_model : StructureModel
        Structure model
    db_sg_model : DataBase
        Database of SG models
    db_sg_model_sets : DataBase, optional
        Database of specific SG models
    """
    logger.debug('='*16)
    logger.info(f'[lv{structure_model.level}:{structure_model.name}] discretizing the design...')

    if structure_model.level > 0 and len(structure_model._design.sg_assignments) == 0:
        return

    logger.debug(f'structure_model._design.sg_assignments = {structure_model._design.sg_assignments}')
    for _sa in structure_model._design.sg_assignments:
        logger.debug(_sa.sg_model)

    try:
        if len(structure_model.design.distributions) == 0:
            structure_model._handle_no_distributions(db_sg_model)
        else:
            process_sg_assignments(structure_model, db_sg_model, db_sg_model_sets)

        # Collect all discrete SG assignments
        for _sa in structure_model._design.sg_assignments:
            structure_model._discrete_sg_assigns.extend(_sa.discrete_sg_assigns)

        # Write mesh data
        structure_model.writeMeshData()

        # Process sub-level SGs
        logger.debug(f'structure_model._sg_model_sets = {structure_model._sg_model_sets}')
        for _name, _sg_model in structure_model._sg_model_sets.items():
            _sg_model.discretizeDesign(db_sg_model, db_sg_model_sets)

    except Exception as e:
        logger.error(f'Error in discretizeDesign: {str(e)}')
        raise


def calc_params_elements_average(
    cells:list[CellBlock],
    cell_data_etags:list[np.ndarray],
    point_data_param:np.ndarray,
    etags_in_region:np.ndarray
    ):
    """Calculate the average parameters for each element.

    For each element, average the parameter values of its nodes.

    Parameters
    ----------
    cells : list of CellBlock
        List of cell blocks.
        Structure:

        ..  code-block::

            [
                CellBlock('triangle', [[0, 1, 2], [1, 2, 3], ...]),
                CellBlock('quad', [[11, 12, 13, 14], [15, 16, 17, 18], ...]),
                ...
            ]

    cell_data_etags : list of ndarray
        List of element IDs for each cell block.
        Structure:

        ..  code-block::

            [
                [1, 2, 3, ...],        # Element IDs for the first cell block
                [101, 102, 103, ...],  # Element IDs for the second cell block
                ...
            ]

    point_data_param : ndarray
        Array of parameter values for each point.
        Structure:

        ..  code-block::

            [value1, value2, ...]

    etags_in_region : ndarray
        Array of element IDs in the region.
        Structure:

        ..  code-block::

            [1, 2, ...]

    Returns
    -------
    ndarray
        Array of parameter values for each element.
        Same structure as `cell_data_etags`.
        Structure:

        ..  code-block::

            [
                [value1, value2, ...],  # Parameter values for the first element block
                [value1, value2, ...],  # Parameter values for the second element block
                ...
            ]

    Example
    -------

    Consider the mesh, point data given in the code below.
    For element 1, the average parameter value is (1.0 + 3.0 + 3.0 + 1.0) / 4 = 2.0.
    For element 2, the average parameter value is (3.0 + 6.0 + 3.0) / 3 = 4.0.
    ..  code-block:: python

        >>> cells = [
        ...     CellBlock('triangle', [[1, 5, 4], [1, 2, 5]]),
        ...     CellBlock('quad', [[0, 1, 4, 3], ])
        ... ]
        >>> cell_data_etags = [
        ...     [2, 3],
        ...     [1]
        ... ]
        >>> point_data_param = np.array([1.0, 3.0, 6.0, 1.0, 3.0, 6.0])
        >>> etags_in_region = [1, 2]
        >>> calc_params_elements_average(cells, cell_data_etags, point_data_param, etags_in_region)
        cell_data_param = [
            [4.0, nan],
            [2.0]
        ]
    """
    # Convert etags_in_region to numpy array for efficient operations
    etags_in_region = np.asarray(etags_in_region)

    # Initialize result list to store parameter values for each cell block
    cell_data_param = []

    # Process each cell block using vectorized operations
    for cell_block, cell_etags in zip(cells, cell_data_etags):
        # Convert cell_etags to numpy array
        cell_etags = np.asarray(cell_etags)

        # Initialize parameter array for this cell block with NaN values
        cell_param_values = np.full(len(cell_etags), np.nan)

        # Vectorized mask: find which elements are in the region
        region_mask = np.isin(cell_etags, etags_in_region)

        if np.any(region_mask):
            # Get indices of elements in the region
            region_element_indices = np.where(region_mask)[0]

            # Get all element connectivity for elements in the region
            region_elements = cell_block.data[region_element_indices]

            # Vectorized parameter averaging using advanced indexing
            # Get parameter values for all nodes of all region elements
            region_node_params = point_data_param[region_elements]  # Shape: (n_region_elements, nodes_per_element)

            # Calculate mean along the node axis (axis=1) for each element
            region_avg_params = np.nanmean(region_node_params, axis=1)

            # Assign the averaged values back to the correct positions
            cell_param_values[region_element_indices] = region_avg_params

        # Add this cell block's parameter values to the result
        cell_data_param.append(cell_param_values)

    return cell_data_param


def group_params_sets(cell_data_params:dict[str, list[np.ndarray]]):
    """Group parameter sets based on identical parameters.

    Parameters
    ----------
    cell_data_params : dict
        Dictionary of lists of parameter values for each element.
        Structure:

        ..  code-block::

            {
                'param1': [
                    [value1, value2, ...],  # Parameter values for the first element block
                    [value1, value2, ...],  # Parameter values for the second element block
                    ...
                ],
                'param2': [
                    [value1, value2, ...],
                    [value1, value2, ...],
                    ...
                ],
                ...
            }

    Returns
    -------
    dict
        Dictionary of parameter sets and set IDs for each element.
        Structure:

        ..  code-block::

            {
                'params_sets': [
                    {'param1': value1, 'param2': value2, ...},  # First parameter set
                    {'param1': value1, 'param2': value2, ...},  # Second parameter set
                    ...
                ],
                'cell_data_set_id': [
                    [set_id1, set_id2, ...],  # Set IDs for the first element block
                    [set_id1, set_id2, ...],  # Set IDs for the second element block
                    ...
                ]
            }

    Example
    -------
    ..  code-block:: python

        >>> cell_data_param = {
        ...     'param1': [
        ...         [1.0, 2.0, 3.0],  # Parameter values for the first element block
        ...         [3.0, nan],       # Parameter values for the second element block
        ...     ],
        ...     'param2': [
        ...         [9, 5, 5],
        ...         [5, nan]
        ...     ]
        ... }
        >>> group_params_sets(cell_data_param)
        {
            'params_sets': [
                {'param1': 1.0, 'param2': 9},  # First parameter set
                {'param1': 2.0, 'param2': 5},  # Second parameter set
                {'param1': 3.0, 'param2': 5},  # Third parameter set
            ],
            'cell_data_set_id': [
                [0, 1, 2],  # Set IDs for the first element block
                [2, nan]      # Set IDs for the second element block
            ]
        }
    """


def discretize_design(
    sg_level:int,
    sg_assignments:list,
    mesh_nodes:list,
    mesh_cells:list[CellBlock],
    cell_data_etags,
    entity_sets,
    distributions:list[Distribution]
    ) -> dict:
    """Calculate the discrete SG designs based on the structural mesh.

    This function uses vectorized operations for faster parameter evaluation
    compared to the old node-by-node approach.

    Parameters
    ----------
    sg_level : int
        Level of the SG.
    sg_assignments : list
        List of SG assignments.
    mesh_nodes : ndarray
        List of all nodes.
    mesh_cells : list of CellBlock
        List of cell blocks.
        Structure:

        ..  code-block::

            [
                CellBlock('triangle', [[0, 1, 2], [1, 2, 3], ...]),
                CellBlock('quad', [[11, 12, 13, 14], [15, 16, 17, 18], ...]),
                ...
            ]
    cell_data_etags : list of ndarray
        List of element IDs for each cell block.
        Structure:

        ..  code-block::

            [
                [1, 2, 3, ...],
                [101, 102, 103, ...],
                ...
            ]
    entity_sets : dict
        Dictionary of entity sets.
    distributions : list
        List of distributions.

    Returns
    -------
    dict
        Discrete SG parameters.
        Structure:

        ..  code-block::

            {
                'entity_sets': {
                    # New entity sets
                    # Elements/nodes with identical SG models
                    'name': EntitySet,
                    ...
                },

                0: {'sg': 'sg1', 'params': {'param1': 1.0, 'param2': 2.0, ...}},
                1: {'sg': 'sg1', 'params': {'param1': 1.0, 'param2': 2.0, ...}},
                ...
            }
    """

    logger.info('Calculating discrete SG designs...')

    # 1. Early return if no SG assignments at sub-levels
    if sg_level > 0 and len(sg_assignments) == 0:
        return {}

    # 2. Get mesh data (nodes, elements, entity sets)
    # _nodes = structure_model.getMeshNodes() or structure_model.mesh_nodes_pd
    # _elements = structure_model.getMeshElements()
    # _entity_sets = structure_model.getEntitySets()

    # 3. Handle case with no distributions
    if len(distributions) == 0:
        # Create default SG assignment to all elements
        # Call structure_model._handle_no_distributions(db_sg_model)
        return {}

    # 4. Handle case with distributions (main vectorized workflow)
    else:
        # Evaluate all distributions for all nodes in their regions
        params_node_values = eval_distributions_on_region_nodes(
            distributions, mesh_cells, cell_data_etags, mesh_nodes, entity_sets
        )
        logger.debug('params_node_values')
        logger.debug(pretty_repr(params_node_values))

        # # 4.1 Group distributions by region for efficient processing
        # region_distributions = {}
        # for distr in distributions:
        #     if distr.region not in region_distributions:
        #         region_distributions[distr.region] = []
        #     region_distributions[distr.region].append(distr)

        # # 4.2 For each region with distributions, vectorize evaluation
        # for region_name, distributions in region_distributions.items():
        #     logger.debug(f'Processing region {region_name}...')
        #     # Get all nodes in this region
        #     # region_nodes = get_region_nodes(region_name, _entity_sets, _elements)

        #     # Vectorized evaluation: eval_distributions_on_region_nodes()
        #     # This replaces the old node-by-node loop with numpy vectorization
        #     region_param_values = eval_distributions_on_region_nodes(
        #         distributions, mesh_cells, cell_data_etags, mesh_nodes, entity_sets
        #     )
        #     logger.debug(f'region_param_values = {region_param_values}')

        #     # Store results in structure_model._discrete_sg_params

        # 4.3 Process SG assignments using vectorized parameter data
        for _sg_assign in sg_assignments:
            if _sg_assign.location == 'node':
                # Vectorized node assignment processing
                pass
            elif _sg_assign.location.startswith('element'):
                # Vectorized element assignment processing
                # Use numpy operations for parameter averaging/grouping
                pass

    # 5. Create entity sets and SG model sets
    # # Group entities with identical parameters using vectorized operations
    # # Update structure_model._entity_sets, _sg_param_sets, _sg_model_sets

    # 6. Finalize assignments and write output
    # # Collect all discrete SG assignments
    # # structure_model.writeMeshData()

    # 7. Recursively process sub-level SGs
    # # for _name, _sg_model in structure_model._sg_model_sets.items():
    # #     _sg_model.discretizeDesign()

    return {}
